/* SellerRequests Component Styles - Following consistent dashboard pattern */
.seller-requests-container {
  padding: 0;
  background-color: transparent;
  font-family: "Poppins", sans-serif;
}

.seller-requests-container .requests-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--smallfont);
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: var(--box-shadow-light);
  overflow-x: auto;
}

.seller-requests-container .requests-table th {
  padding: 12px 10px;
  text-align: left;
  vertical-align: middle;
  background-color: var(--bg-gray);
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--extrasmallfont);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--light-gray);
}

.seller-requests-container .requests-table td {
  padding: 12px 10px;
  text-align: left;
  border-bottom: 1px solid var(--light-gray);
  vertical-align: middle;
}

.seller-requests-container .requests-table tbody tr {
  transition: background-color 0.2s ease;
}

.seller-requests-container .requests-table tbody tr:hover {
  background-color: var(--primary-light-color);
}

.seller-requests-container .requests-table tbody tr:last-child td {
  border-bottom: none;
}

.seller-requests-container .video-doc {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.seller-requests-container .video-doc img {
  width: 55px;
  height: 55px;
  border-radius: var(--border-radius);
  object-fit: cover;
}

.seller-requests-container .video-doc span {
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--text-color);
}

/* Action icons - Following consistent action button pattern */
.seller-requests-container .action-icons {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

.seller-requests-container .eye-icon,
.seller-requests-container .comment-icon {
  font-size: var(--basefont);
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius);
}

.seller-requests-container .action-icon:hover,
.seller-requests-container .eye-icon:hover,
.seller-requests-container .comment-icon:hover {
  color: var(--primary-color);
  background-color: var(--primary-light-color);
  transform: scale(1.05);
}

/* Enhanced Requests Styles - Following consistent dashboard pattern */
.seller-requests-container .requests-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--heading4);
  gap: var(--heading5);
  padding: var(--basefont);
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
}

.seller-requests-container .requests-stats {
  display: flex;
  gap: var(--heading5);
}

.seller-requests-container .stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--basefont);
  background: var(--white);
  border-radius: var(--border-radius);
  min-width: 100px;
  box-shadow: var(--box-shadow-light);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.seller-requests-container .stat-item:hover {
  transform: scale(1.02);
  box-shadow: var(--box-shadow);
}

.seller-requests-container .stat-icon {
  font-size: var(--heading4);
  color: var(--primary-color);
  margin-bottom: var(--extrasmallfont);
}

.seller-requests-container .stat-count {
  font-size: var(--heading4);
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: var(--extrasmallfont);
}

.seller-requests-container .stat-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  text-align: center;
}

.seller-requests-container .requests-filters {
  display: flex;
  gap: var(--basefont);
  align-items: center;
  padding: var(--basefont);
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  margin-bottom: var(--heading5);
}

.seller-requests-container .filter-group,
.seller-requests-container .search-group {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.seller-requests-container .filter-select,
.seller-requests-container .search-input {
  padding: 10px var(--smallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  background: var(--white);
  transition: all 0.3s ease;
}

.seller-requests-container .filter-select:focus,
.seller-requests-container .search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(236, 29, 59, 0.1);
}

.seller-requests-container .search-input {
  min-width: 200px;
}

.seller-requests-container .requests-table-container {
  background: var(--white);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: var(--box-shadow-light);
  border: 1px solid var(--light-gray);
}

.seller-requests-container .request-details {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.seller-requests-container .request-title {
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: var(--extrasmallfont);
}

.seller-requests-container .request-meta {
  display: flex;
  gap: var(--border-radius-medium);
}

.seller-requests-container .content-type,
.seller-requests-container .sport {
  font-size: var(--smallfont);
  padding: var(--extrasmallfont) var(--border-radius-medium);
  background: var(--bg-gray);
  border-radius: var(--border-radius);
  color: var(--dark-gray);
}

.seller-requests-container .buyer-info {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.seller-requests-container .buyer-name {
  font-weight: 500;
  color: var(--secondary-color);
}

.seller-requests-container .buyer-email {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.seller-requests-container .budget-info {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
}

.seller-requests-container .budget-amount {
  font-weight: 600;
  color: var(--primary-color);
  font-size: var(--heading6);
}

/* Status badges - Following consistent badge pattern */
.seller-requests-container .status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: 4px var(--smallfont);
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-transform: capitalize;
}

.seller-requests-container .status-orange {
  background: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.seller-requests-container .status-green {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.seller-requests-container .status-red {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.seller-requests-container .status-blue {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.seller-requests-container .status-purple {
  background: rgba(155, 89, 182, 0.1);
  color: #9b59b6;
}

.seller-requests-container .status-gray {
  background: var(--bg-gray);
  color: var(--dark-gray);
}

/* Action buttons - Following consistent dashboard pattern */
.seller-requests-container .action-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

.seller-requests-container .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: var(--basefont);
}

.seller-requests-container .view-btn {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.seller-requests-container .view-btn:hover {
  background: #3498db;
  color: var(--white);
  transform: scale(1.05);
}

.seller-requests-container .respond-btn {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.seller-requests-container .respond-btn:hover {
  background: #2ecc71;
  color: var(--white);
  transform: scale(1.05);
}

.seller-requests-container .payment-btn {
  background: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.seller-requests-container .payment-btn:hover {
  background: #f39c12;
  color: var(--white);
  transform: scale(1.05);
}

/* Empty state - Following consistent empty state pattern */
.seller-requests-container .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading2);
  text-align: center;
  background: var(--primary-light-color);
  border-radius: var(--border-radius-large);
  border: 2px dashed var(--light-gray);
}

.seller-requests-container .empty-icon {
  font-size: var(--heading1);
  color: var(--light-gray);
  margin-bottom: var(--heading5);
}

.seller-requests-container .empty-state h3 {
  margin: 0 0 var(--smallfont) 0;
  color: var(--secondary-color);
  font-size: var(--heading5);
  font-weight: 600;
}

.seller-requests-container .empty-state p {
  margin: 0;
  color: var(--dark-gray);
  font-size: var(--basefont);
  max-width: 400px;
  line-height: 1.5;
}

/* Responsive Design - Following consistent responsive pattern */
@media (max-width: 1024px) {
  .seller-requests-container .requests-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--basefont);
  }

  .seller-requests-container .requests-filters {
    justify-content: space-between;
    gap: var(--smallfont);
  }
}

@media (max-width: 768px) {
  .seller-requests-container .requests-header,
  .seller-requests-container .requests-filters {
    padding: var(--smallfont);
  }

  .seller-requests-container .requests-stats {
    flex-wrap: wrap;
    gap: var(--smallfont);
  }

  .seller-requests-container .stat-item {
    min-width: 80px;
    padding: var(--smallfont);
  }

  .seller-requests-container .requests-filters {
    flex-direction: column;
    gap: var(--smallfont);
  }

  .seller-requests-container .search-input {
    min-width: auto;
  }

  .seller-requests-container .requests-table {
    font-size: var(--extrasmallfont);
  }

  .seller-requests-container .requests-table th,
  .seller-requests-container .requests-table td {
    padding: 8px 6px;
  }

  .seller-requests-container .action-buttons {
    gap: 2px;
  }

  .seller-requests-container .action-btn {
    width: 28px;
    height: 28px;
    font-size: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .seller-requests-container .requests-table {
    overflow-x: auto;
  }

  .seller-requests-container .requests-header,
  .seller-requests-container .requests-filters {
    padding: var(--extrasmallfont);
  }

  .seller-requests-container .stat-item {
    min-width: 70px;
    padding: var(--extrasmallfont);
  }

  .seller-requests-container .stat-count {
    font-size: var(--heading5);
  }

  .seller-requests-container .empty-state {
    padding: var(--heading5);
  }
}
